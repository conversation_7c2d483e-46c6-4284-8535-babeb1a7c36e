<?php
/**
 * WooCommerce Featured Video functionality
 * 
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WooCustom Featured Video Class
 */
class WooCustom_Featured_Video {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Admin hooks for product edit page
        add_action('woocommerce_product_options_general_product_data', array($this, 'add_video_fields'));
        add_action('woocommerce_admin_process_product_object', array($this, 'save_video_fields'), 10, 1);

        // Frontend hooks for displaying video in gallery
        add_filter('woocommerce_single_product_image_gallery_classes', array($this, 'add_video_gallery_classes'));
        add_action('wp_footer', array($this, 'add_video_gallery_script'));

        // Admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));

        // Frontend scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'frontend_enqueue_scripts'));

        // AJAX handlers for video management
        add_action('wp_ajax_woo_custom_add_video', array($this, 'ajax_add_video'));
        add_action('wp_ajax_woo_custom_remove_video', array($this, 'ajax_remove_video'));
        add_action('wp_ajax_woo_custom_reorder_videos', array($this, 'ajax_reorder_videos'));

        // Add video gallery button to product images metabox
        add_action('add_meta_boxes', array($this, 'add_video_gallery_metabox'), 20);
    }
    
    /**
     * Add video fields to product general tab
     */
    public function add_video_fields() {
        global $post;
        
        echo '<div class="options_group">';
        
        // Video URL field
        woocommerce_wp_text_input(array(
            'id' => '_woo_custom_video_url',
            'label' => __('Featured Video URL', 'woo-custom'),
            'placeholder' => __('YouTube veya Vimeo video URL\'si girin', 'woo-custom'),
            'desc_tip' => true,
            'description' => __('Ürün sayfasında gösterilecek video URL\'sini girin. YouTube ve Vimeo desteklenir.', 'woo-custom'),
            'type' => 'url'
        ));
        
        // Aspect ratio field
        woocommerce_wp_select(array(
            'id' => '_woo_custom_video_aspect_ratio',
            'label' => __('Video En Boy Oranı', 'woo-custom'),
            'desc_tip' => true,
            'description' => __('Video için en boy oranını seçin.', 'woo-custom'),
            'options' => array(
                '16:9' => __('16:9 (Geniş ekran)', 'woo-custom'),
                '4:3' => __('4:3 (Standart)', 'woo-custom'),
            ),
            'value' => get_post_meta($post->ID, '_woo_custom_video_aspect_ratio', true) ?: '16:9'
        ));
        
        echo '</div>';
    }

    /**
     * Add video gallery metabox content to product images
     */
    public function add_video_gallery_metabox() {
        global $post;

        if (!$post || $post->post_type !== 'product') {
            return;
        }

        // Add content after product gallery
        add_action('admin_footer', array($this, 'render_video_gallery_section'));
    }

    /**
     * Render video gallery section
     */
    public function render_video_gallery_section() {
        global $post;

        if (!$post || $post->post_type !== 'product') {
            return;
        }

        $videos = $this->get_product_videos($post->ID);
        ?>
        <div id="woo-custom-video-gallery-section" class="postbox" style="display: none;">
            <div class="postbox-header">
                <h2 class="hndle"><?php _e('Urun Videolari', 'woo-custom'); ?></h2>
            </div>
            <div class="inside">
                <div class="woo-custom-video-gallery-controls">
                    <button type="button" id="add-video-btn" class="button button-primary">
                        <?php _e('Video Linki Yukle', 'woo-custom'); ?>
                    </button>
                </div>
                <ul id="woo-custom-video-gallery-list" class="woo-custom-video-gallery-list">
                    <?php
                    foreach ($videos as $video) {
                        echo $this->render_video_gallery_item($video);
                    }
                    ?>
                </ul>
            </div>
        </div>

        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Move video gallery section after product images
            var $productImages = $('#woocommerce-product-images');
            var $videoSection = $('#woo-custom-video-gallery-section');

            if ($productImages.length && $videoSection.length) {
                $videoSection.insertAfter($productImages).show();
            }
        });
        </script>

        <!-- Video Modal -->
        <div id="woo-custom-video-modal" class="woo-custom-modal" style="display: none;">
            <div class="woo-custom-modal-content">
                <div class="woo-custom-modal-header">
                    <h3><?php _e('Video Ekle', 'woo-custom'); ?></h3>
                    <button type="button" class="woo-custom-modal-close">&times;</button>
                </div>
                <div class="woo-custom-modal-body">
                    <form id="woo-custom-video-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="video_url"><?php _e('Video URL', 'woo-custom'); ?></label>
                                </th>
                                <td>
                                    <input type="url" id="video_url" name="video_url" class="regular-text"
                                           placeholder="<?php _e('YouTube veya Vimeo video URL\'si girin', 'woo-custom'); ?>" required>
                                    <p class="description"><?php _e('Urun sayfasinda gosterilecek video URL\'sini girin. YouTube ve Vimeo desteklenir.', 'woo-custom'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="video_aspect_ratio"><?php _e('Video En Boy Orani', 'woo-custom'); ?></label>
                                </th>
                                <td>
                                    <select id="video_aspect_ratio" name="video_aspect_ratio">
                                        <option value="16:9"><?php _e('16:9 (Genis ekran)', 'woo-custom'); ?></option>
                                        <option value="4:3"><?php _e('4:3 (Standart)', 'woo-custom'); ?></option>
                                    </select>
                                    <p class="description"><?php _e('Video icin en boy oranini secin.', 'woo-custom'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="video_title"><?php _e('Video Basligi', 'woo-custom'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="video_title" name="video_title" class="regular-text"
                                           placeholder="<?php _e('Opsiyonel video basligi', 'woo-custom'); ?>">
                                    <p class="description"><?php _e('Video icin opsiyonel baslik.', 'woo-custom'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>
                <div class="woo-custom-modal-footer">
                    <button type="button" class="button button-primary" id="add-video-submit">
                        <?php _e('Ekle', 'woo-custom'); ?>
                    </button>
                    <button type="button" class="button" id="add-video-cancel">
                        <?php _e('Iptal', 'woo-custom'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Save video fields when product is saved
     */
    public function save_video_fields($product) {
        // Save video URL (backward compatibility)
        if (isset($_POST['_woo_custom_video_url'])) {
            $video_url = sanitize_url($_POST['_woo_custom_video_url']);
            $product->update_meta_data('_woo_custom_video_url', $video_url);
        }

        // Save aspect ratio (backward compatibility)
        if (isset($_POST['_woo_custom_video_aspect_ratio'])) {
            $aspect_ratio = sanitize_text_field($_POST['_woo_custom_video_aspect_ratio']);
            $product->update_meta_data('_woo_custom_video_aspect_ratio', $aspect_ratio);
        }

        // Save multiple videos data
        if (isset($_POST['_woo_custom_videos_data'])) {
            $videos_data = json_decode(stripslashes($_POST['_woo_custom_videos_data']), true);
            if (is_array($videos_data)) {
                $sanitized_videos = array();
                foreach ($videos_data as $video) {
                    if (isset($video['url']) && isset($video['aspect_ratio'])) {
                        $sanitized_videos[] = array(
                            'id' => isset($video['id']) ? sanitize_text_field($video['id']) : uniqid('video_'),
                            'url' => sanitize_url($video['url']),
                            'aspect_ratio' => sanitize_text_field($video['aspect_ratio']),
                            'title' => isset($video['title']) ? sanitize_text_field($video['title']) : '',
                            'order' => isset($video['order']) ? intval($video['order']) : 0
                        );
                    }
                }
                $product->update_meta_data('_woo_custom_videos_data', $sanitized_videos);
            }
        }
    }

    /**
     * Get all videos for a product
     */
    public function get_product_videos($product_id) {
        $videos_data = get_post_meta($product_id, '_woo_custom_videos_data', true);

        if (empty($videos_data) || !is_array($videos_data)) {
            // Check for legacy single video
            $legacy_url = get_post_meta($product_id, '_woo_custom_video_url', true);
            $legacy_aspect = get_post_meta($product_id, '_woo_custom_video_aspect_ratio', true);

            if (!empty($legacy_url)) {
                return array(
                    array(
                        'id' => 'legacy_video',
                        'url' => $legacy_url,
                        'aspect_ratio' => $legacy_aspect ?: '16:9',
                        'title' => '',
                        'order' => 0
                    )
                );
            }

            return array();
        }

        // Sort by order
        usort($videos_data, function($a, $b) {
            return ($a['order'] ?? 0) - ($b['order'] ?? 0);
        });

        return $videos_data;
    }



    /**
     * Add video gallery classes
     */
    public function add_video_gallery_classes($classes) {
        global $product;

        if ($product) {
            $videos = $this->get_product_videos($product->get_id());
            if (!empty($videos)) {
                $classes[] = 'woo-custom-has-videos';
            }
        }

        return $classes;
    }

    /**
     * Add video gallery script to footer
     */
    public function add_video_gallery_script() {
        if (!is_product()) {
            return;
        }

        global $product;
        if (!$product) {
            return;
        }

        $videos = $this->get_product_videos($product->get_id());
        if (empty($videos)) {
            return;
        }

        // Generate video HTML for JavaScript
        $videos_data = array();
        foreach ($videos as $video) {
            $embed_url = $this->get_video_embed_url($video['url']);
            if (!$embed_url) {
                continue;
            }

            $thumbnail_url = $this->get_video_thumbnail($video['url']);
            $aspect_class = $video['aspect_ratio'] === '4:3' ? 'aspect-4-3' : 'aspect-16-9';
            $video_title = !empty($video['title']) ? $video['title'] : 'Video';

            $videos_data[] = array(
                'id' => $video['id'],
                'url' => $video['url'],
                'embed_url' => $embed_url,
                'thumbnail_url' => $thumbnail_url,
                'aspect_class' => $aspect_class,
                'title' => $video_title
            );
        }

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            var videosData = <?php echo json_encode($videos_data); ?>;

            function addVideosToGallery() {
                var $gallery = $('.woocommerce-product-gallery');
                var $flexViewport = $gallery.find('.flex-viewport');
                var $slides = $flexViewport.find('ul.slides');

                if ($slides.length === 0) {
                    return;
                }

                // Add videos to slides
                $.each(videosData, function(index, video) {
                    var videoHtml = '<li class="woocommerce-product-gallery__image woo-custom-featured-video-gallery" data-thumb="' + video.thumbnail_url + '" data-video-id="' + video.id + '">' +
                        '<a href="' + video.url + '" data-video-url="' + video.url + '">' +
                            '<div class="woo-custom-featured-video ' + video.aspect_class + '" data-video-title="' + video.title + '">' +
                                '<div class="video-container">' +
                                    '<iframe src="' + video.embed_url + '" frameborder="0" allowfullscreen allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>' +
                                '</div>' +
                            '</div>' +
                        '</a>' +
                    '</li>';

                    $slides.append(videoHtml);
                });

                // Reinitialize flexslider
                if ($gallery.data('flexslider')) {
                    $gallery.flexslider('destroy');
                }

                // Reinitialize WooCommerce gallery
                setTimeout(function() {
                    $gallery.wc_product_gallery();
                }, 100);
            }

            // Try multiple times to ensure gallery is ready
            setTimeout(addVideosToGallery, 500);
            setTimeout(addVideosToGallery, 1000);
            setTimeout(addVideosToGallery, 2000);
        });
        </script>
        <?php
    }

    /**
     * Convert video URL to embed URL
     */
    public function get_video_embed_url($url) {
        // YouTube URL patterns
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $url, $matches)) {
            return 'https://www.youtube.com/embed/' . $matches[1] . '?rel=0&showinfo=0&enablejsapi=1';
        }

        // Vimeo URL patterns
        if (preg_match('/vimeo\.com\/(\d+)/', $url, $matches)) {
            return 'https://player.vimeo.com/video/' . $matches[1];
        }

        return false;
    }

    /**
     * Get video thumbnail URL
     */
    public function get_video_thumbnail($url) {
        // YouTube thumbnail
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $url, $matches)) {
            return 'https://img.youtube.com/vi/' . $matches[1] . '/maxresdefault.jpg';
        }

        // Vimeo thumbnail (simplified - in real implementation you'd need API call)
        if (preg_match('/vimeo\.com\/(\d+)/', $url, $matches)) {
            // For now, return a placeholder. In production, you'd use Vimeo API
            return WOO_CUSTOM_PLUGIN_URL . 'assets/images/video-placeholder.svg';
        }

        return WOO_CUSTOM_PLUGIN_URL . 'assets/images/video-placeholder.svg';
    }

    /**
     * AJAX handler for adding video
     */
    public function ajax_add_video() {
        check_ajax_referer('woo_custom_video_nonce', 'nonce');

        if (!current_user_can('edit_products')) {
            wp_die(__('Yetkiniz yok.', 'woo-custom'));
        }

        $product_id = intval($_POST['product_id']);
        $video_url = sanitize_url($_POST['video_url']);
        $aspect_ratio = sanitize_text_field($_POST['aspect_ratio']);
        $title = sanitize_text_field($_POST['title'] ?? '');

        if (empty($video_url)) {
            wp_send_json_error(__('Video URL gerekli.', 'woo-custom'));
        }

        $videos = $this->get_product_videos($product_id);
        $new_video = array(
            'id' => uniqid('video_'),
            'url' => $video_url,
            'aspect_ratio' => $aspect_ratio,
            'title' => $title,
            'order' => count($videos)
        );

        $videos[] = $new_video;
        update_post_meta($product_id, '_woo_custom_videos_data', $videos);

        wp_send_json_success(array(
            'video' => $new_video,
            'html' => $this->render_video_gallery_item($new_video)
        ));
    }

    /**
     * AJAX handler for removing video
     */
    public function ajax_remove_video() {
        check_ajax_referer('woo_custom_video_nonce', 'nonce');

        if (!current_user_can('edit_products')) {
            wp_die(__('Yetkiniz yok.', 'woo-custom'));
        }

        $product_id = intval($_POST['product_id']);
        $video_id = sanitize_text_field($_POST['video_id']);

        $videos = $this->get_product_videos($product_id);
        $videos = array_filter($videos, function($video) use ($video_id) {
            return $video['id'] !== $video_id;
        });

        // Reorder
        $videos = array_values($videos);
        foreach ($videos as $index => &$video) {
            $video['order'] = $index;
        }

        update_post_meta($product_id, '_woo_custom_videos_data', $videos);

        wp_send_json_success();
    }

    /**
     * AJAX handler for reordering videos
     */
    public function ajax_reorder_videos() {
        check_ajax_referer('woo_custom_video_nonce', 'nonce');

        if (!current_user_can('edit_products')) {
            wp_die(__('Yetkiniz yok.', 'woo-custom'));
        }

        $product_id = intval($_POST['product_id']);
        $video_ids = array_map('sanitize_text_field', $_POST['video_ids']);

        $videos = $this->get_product_videos($product_id);
        $reordered_videos = array();

        foreach ($video_ids as $index => $video_id) {
            foreach ($videos as $video) {
                if ($video['id'] === $video_id) {
                    $video['order'] = $index;
                    $reordered_videos[] = $video;
                    break;
                }
            }
        }

        update_post_meta($product_id, '_woo_custom_videos_data', $reordered_videos);

        wp_send_json_success();
    }

    /**
     * Render video gallery item HTML
     */
    private function render_video_gallery_item($video) {
        $thumbnail_url = $this->get_video_thumbnail($video['url']);
        $embed_url = $this->get_video_embed_url($video['url']);

        ob_start();
        ?>
        <li class="woo-custom-video-gallery-item" data-video-id="<?php echo esc_attr($video['id']); ?>">
            <div class="video-thumbnail">
                <img src="<?php echo esc_url($thumbnail_url); ?>" alt="<?php echo esc_attr($video['title']); ?>">
                <div class="video-overlay">
                    <span class="dashicons dashicons-controls-play"></span>
                </div>
            </div>
            <div class="video-actions">
                <button type="button" class="button-link remove-video" title="<?php _e('Videoyu Sil', 'woo-custom'); ?>">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
        </li>
        <?php
        return ob_get_clean();
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        // Only load on product edit pages
        if ($hook !== 'post.php' && $hook !== 'post-new.php') {
            return;
        }

        global $post;
        if (!$post || $post->post_type !== 'product') {
            return;
        }

        wp_enqueue_style(
            'woo-custom-featured-video-admin',
            WOO_CUSTOM_PLUGIN_URL . 'assets/css/featured-video-admin.css',
            array(),
            WOO_CUSTOM_VERSION
        );

        wp_enqueue_script(
            'woo-custom-featured-video-admin',
            WOO_CUSTOM_PLUGIN_URL . 'assets/js/featured-video-admin.js',
            array('jquery', 'jquery-ui-sortable'),
            WOO_CUSTOM_VERSION,
            true
        );

        wp_localize_script('woo-custom-featured-video-admin', 'wooCustomVideo', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('woo_custom_video_nonce'),
            'product_id' => $post->ID,
            'strings' => array(
                'add_video' => __('Video Linki Yukle', 'woo-custom'),
                'video_url' => __('Video URL', 'woo-custom'),
                'aspect_ratio' => __('En Boy Orani', 'woo-custom'),
                'video_title' => __('Video Basligi (Opsiyonel)', 'woo-custom'),
                'add_button' => __('Ekle', 'woo-custom'),
                'cancel_button' => __('Iptal', 'woo-custom'),
                'remove_confirm' => __('Bu videoyu silmek istediginizden emin misiniz?', 'woo-custom'),
                'error_url_required' => __('Video URL gerekli.', 'woo-custom'),
                'error_invalid_url' => __('Gecerli bir YouTube veya Vimeo URL\'si girin.', 'woo-custom')
            )
        ));
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function frontend_enqueue_scripts() {
        if (is_product()) {
            wp_enqueue_style(
                'woo-custom-featured-video-frontend',
                WOO_CUSTOM_PLUGIN_URL . 'assets/css/featured-video-frontend.css',
                array(),
                WOO_CUSTOM_VERSION
            );

            wp_enqueue_script(
                'woo-custom-featured-video-frontend',
                WOO_CUSTOM_PLUGIN_URL . 'assets/js/featured-video-frontend.js',
                array('jquery'),
                WOO_CUSTOM_VERSION,
                true
            );
        }
    }
}
