/**
 * WooCustom Featured Video Admin Styles
 * 
 * @package WooCustom
 * @since 1.0.0
 */

/* Video fields styling in product admin */
.options_group .form-field._woo_custom_video_url_field,
.options_group .form-field._woo_custom_video_aspect_ratio_field {
    margin-bottom: 15px;
}

.options_group .form-field._woo_custom_video_url_field label,
.options_group .form-field._woo_custom_video_aspect_ratio_field label {
    font-weight: 600;
    color: #23282d;
}

.options_group .form-field._woo_custom_video_url_field input[type="url"] {
    width: 100%;
    max-width: 400px;
}

.options_group .form-field._woo_custom_video_aspect_ratio_field select {
    width: 200px;
}

/* Help text styling */
.options_group .form-field .woocommerce-help-tip {
    margin-left: 5px;
}

/* Video Gallery Section */
#woo-custom-video-gallery-section {
    margin-top: 20px;
}

.woo-custom-video-gallery-controls {
    margin-bottom: 15px;
}

#add-video-btn {
    background: #0073aa;
    border-color: #0073aa;
    color: white !important;
    text-decoration: none !important;
    border-radius: 3px;
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.4;
    cursor: pointer;
    border: 1px solid #0073aa;
    display: inline-block;
}

#add-video-btn:hover,
#add-video-btn:focus {
    background: #005a87 !important;
    border-color: #005a87 !important;
    color: white !important;
}

.woo-custom-video-gallery-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.woo-custom-video-gallery-item {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    cursor: move;
}

.woo-custom-video-gallery-item:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 100px;
    overflow: hidden;
    background: #f5f5f5;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.7);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-overlay .dashicons {
    color: white;
    font-size: 20px;
}

.video-actions {
    position: absolute;
    top: 5px;
    right: 5px;
}

.remove-video {
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #dc3232;
}

.remove-video:hover {
    background: #dc3232;
    color: white;
}

.video-placeholder {
    background: #f0f0f0;
    border: 2px dashed #ccc;
    height: 100px;
    border-radius: 8px;
}

/* Modal Styles */
.woo-custom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.woo-custom-modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.woo-custom-modal-header {
    padding: 20px 20px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.woo-custom-modal-header h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
}

.woo-custom-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.woo-custom-modal-close:hover {
    color: #000;
}

.woo-custom-modal-body {
    padding: 20px;
}

.woo-custom-modal-footer {
    padding: 0 20px 20px;
    text-align: right;
    border-top: 1px solid #eee;
    margin-top: 20px;
    padding-top: 20px;
}

.woo-custom-modal-footer .button {
    margin-left: 10px;
}

/* Form styling in modal */
.woo-custom-modal .form-table th {
    width: 150px;
    padding: 15px 10px 15px 0;
}

.woo-custom-modal .form-table td {
    padding: 15px 0;
}

.woo-custom-modal .regular-text {
    width: 100%;
    max-width: 400px;
}

.woo-custom-modal .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Video preview area (if needed for future enhancement) */
.woo-custom-video-preview {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
    display: none;
}

.woo-custom-video-preview.active {
    display: block;
}

.woo-custom-video-preview iframe {
    width: 100%;
    height: 200px;
    border: none;
    border-radius: 4px;
}
