<?php
/**
 * Debug script for video gallery issues
 */

// Include WordPress
require_once('wp-config.php');

// Get product ID from URL parameter
$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;

if (!$product_id) {
    $product_id = get_option('woo_custom_test_product_id');
}

if (!$product_id) {
    die('No product ID provided. Use ?product_id=123 or run test-video-gallery.php first.');
}

$product = wc_get_product($product_id);
if (!$product) {
    die('Product not found: ' . $product_id);
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Video Gallery Debug - Product <?php echo $product_id; ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Video Gallery Debug - Product <?php echo $product_id; ?></h1>
    
    <div class="debug-section">
        <h2>Product Information</h2>
        <p><strong>Product ID:</strong> <?php echo $product_id; ?></p>
        <p><strong>Product Name:</strong> <?php echo $product->get_name(); ?></p>
        <p><strong>Product URL:</strong> <a href="<?php echo get_permalink($product_id); ?>" target="_blank"><?php echo get_permalink($product_id); ?></a></p>
    </div>
    
    <div class="debug-section">
        <h2>Video Data</h2>
        <?php
        $video_class = new WooCustom_Featured_Video();
        $videos = $video_class->get_product_videos($product_id);
        
        echo '<p><strong>Videos found:</strong> ' . count($videos) . '</p>';
        
        if (!empty($videos)) {
            echo '<pre>' . print_r($videos, true) . '</pre>';
            
            echo '<h3>Video Processing Test</h3>';
            foreach ($videos as $index => $video) {
                echo '<h4>Video ' . ($index + 1) . ':</h4>';
                echo '<p><strong>URL:</strong> ' . $video['url'] . '</p>';
                
                // Test embed URL generation
                $embed_url = $video_class->get_video_embed_url($video['url']);
                if ($embed_url) {
                    echo '<p class="success">✓ Embed URL: ' . $embed_url . '</p>';
                } else {
                    echo '<p class="error">✗ Could not generate embed URL</p>';
                }
                
                // Test thumbnail URL generation
                $thumbnail_url = $video_class->get_video_thumbnail($video['url']);
                echo '<p class="info">Thumbnail URL: ' . $thumbnail_url . '</p>';
                
                // Test thumbnail accessibility
                $headers = @get_headers($thumbnail_url);
                if ($headers && strpos($headers[0], '200') !== false) {
                    echo '<p class="success">✓ Thumbnail accessible</p>';
                } else {
                    echo '<p class="error">✗ Thumbnail not accessible</p>';
                }
            }
        } else {
            echo '<p class="error">No videos found for this product.</p>';
            
            // Check legacy video data
            $legacy_url = get_post_meta($product_id, '_woo_custom_video_url', true);
            $legacy_aspect = get_post_meta($product_id, '_woo_custom_video_aspect_ratio', true);
            
            if ($legacy_url) {
                echo '<p class="info">Legacy video found: ' . $legacy_url . ' (' . $legacy_aspect . ')</p>';
            } else {
                echo '<p class="error">No legacy video data found either.</p>';
            }
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>WordPress Hooks Test</h2>
        <?php
        echo '<p><strong>Current page:</strong> ' . (is_product() ? 'Product page' : 'Not product page') . '</p>';
        
        $hooks_to_check = [
            'wp_footer' => 'add_video_gallery_script',
            'woocommerce_single_product_image_gallery_classes' => 'add_video_gallery_classes'
        ];
        
        foreach ($hooks_to_check as $hook => $method) {
            if (has_action($hook) || has_filter($hook)) {
                echo '<p class="success">✓ Hook ' . $hook . ' is registered</p>';
            } else {
                echo '<p class="error">✗ Hook ' . $hook . ' not registered</p>';
            }
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Frontend Test</h2>
        <p>Open the product page and check browser console for:</p>
        <ul>
            <li>JavaScript errors</li>
            <li>Video data being loaded</li>
            <li>Gallery initialization</li>
            <li>Video elements being added to DOM</li>
        </ul>
        
        <h3>Manual Test</h3>
        <p>On the product page, open browser console and run:</p>
        <pre>
console.log('Gallery:', jQuery('.woocommerce-product-gallery'));
console.log('Slides:', jQuery('.woocommerce-product-gallery .slides'));
console.log('Video elements:', jQuery('.woo-custom-featured-video-gallery'));
console.log('Flexslider data:', jQuery('.woocommerce-product-gallery').data('flexslider'));
        </pre>
    </div>
    
    <div class="debug-section">
        <h2>Quick Fix Test</h2>
        <p>Try adding a video manually via admin panel:</p>
        <ol>
            <li>Go to <a href="<?php echo admin_url('post.php?post=' . $product_id . '&action=edit'); ?>" target="_blank">Edit Product</a></li>
            <li>Scroll down to "Ürün Videoları" section</li>
            <li>Click "Video Linki Yükle"</li>
            <li>Add: https://www.youtube.com/watch?v=dQw4w9WgXcQ</li>
            <li>Save and check frontend</li>
        </ol>
    </div>
    
    <div class="debug-section">
        <h2>File Permissions</h2>
        <?php
        $files_to_check = [
            WOO_CUSTOM_PLUGIN_DIR . 'assets/js/featured-video-frontend.js',
            WOO_CUSTOM_PLUGIN_DIR . 'assets/css/featured-video-frontend.css'
        ];
        
        foreach ($files_to_check as $file) {
            if (file_exists($file)) {
                if (is_readable($file)) {
                    echo '<p class="success">✓ ' . basename($file) . ' is readable</p>';
                } else {
                    echo '<p class="error">✗ ' . basename($file) . ' is not readable</p>';
                }
            } else {
                echo '<p class="error">✗ ' . basename($file) . ' does not exist</p>';
            }
        }
        ?>
    </div>
    
</body>
</html>
