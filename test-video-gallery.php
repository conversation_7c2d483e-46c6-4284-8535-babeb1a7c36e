<?php
/**
 * Test file for WooCustom Video Gallery functionality
 * 
 * This file can be used to test the video gallery features
 * Place this in your WordPress root and access via browser
 */

// Include WordPress
require_once('wp-config.php');

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    die('WooC<PERSON>merce is not active!');
}

// Check if our plugin is active
if (!class_exists('WooCustom_Featured_Video')) {
    die('WooCustom plugin is not active!');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WooCustom Video Gallery Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>WooCustom Video Gallery Test</h1>
    
    <div class="test-section">
        <h2>1. Plugin Status</h2>
        <?php
        echo '<p class="success">✓ WooCommerce is active</p>';
        echo '<p class="success">✓ WooCustom plugin is active</p>';
        echo '<p class="success">✓ WooCustom_Featured_Video class exists</p>';
        ?>
    </div>
    
    <div class="test-section">
        <h2>2. File Structure Check</h2>
        <?php
        $files_to_check = [
            'includes/class-woo-custom-featured-video.php',
            'assets/js/featured-video-admin.js',
            'assets/css/featured-video-admin.css',
            'assets/js/featured-video-frontend.js',
            'assets/css/featured-video-frontend.css'
        ];
        
        foreach ($files_to_check as $file) {
            $full_path = WOO_CUSTOM_PLUGIN_DIR . $file;
            if (file_exists($full_path)) {
                echo '<p class="success">✓ ' . $file . ' exists</p>';
            } else {
                echo '<p class="error">✗ ' . $file . ' missing</p>';
            }
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>3. Class Methods Check</h2>
        <?php
        $video_class = new WooCustom_Featured_Video();
        $methods_to_check = [
            'get_product_videos',
            'ajax_add_video',
            'ajax_remove_video',
            'ajax_reorder_videos',
            'render_video_gallery_section'
        ];
        
        foreach ($methods_to_check as $method) {
            if (method_exists($video_class, $method)) {
                echo '<p class="success">✓ Method ' . $method . ' exists</p>';
            } else {
                echo '<p class="error">✗ Method ' . $method . ' missing</p>';
            }
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>4. WordPress Hooks Check</h2>
        <?php
        $hooks_to_check = [
            'woocommerce_product_options_general_product_data',
            'woocommerce_admin_process_product_object',
            'wp_ajax_woo_custom_add_video',
            'wp_ajax_woo_custom_remove_video',
            'wp_ajax_woo_custom_reorder_videos'
        ];
        
        foreach ($hooks_to_check as $hook) {
            if (has_action($hook)) {
                echo '<p class="success">✓ Hook ' . $hook . ' is registered</p>';
            } else {
                echo '<p class="error">✗ Hook ' . $hook . ' not registered</p>';
            }
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>5. Test Product Creation</h2>
        <?php
        // Create a test product if it doesn't exist
        $test_product_id = get_option('woo_custom_test_product_id');
        
        if (!$test_product_id || !get_post($test_product_id)) {
            $product = new WC_Product_Simple();
            $product->set_name('Test Video Product');
            $product->set_status('publish');
            $product->set_catalog_visibility('visible');
            $product->set_description('Test product for video gallery functionality');
            $product->set_short_description('Test product');
            $product->set_sku('test-video-product');
            $product->set_price(10);
            $product->set_regular_price(10);
            $product->set_manage_stock(false);
            $product->set_stock_status('instock');
            
            $test_product_id = $product->save();
            update_option('woo_custom_test_product_id', $test_product_id);
            
            echo '<p class="success">✓ Test product created with ID: ' . $test_product_id . '</p>';
        } else {
            echo '<p class="info">ℹ Test product already exists with ID: ' . $test_product_id . '</p>';
        }
        
        // Test video data
        $test_videos = [
            [
                'id' => 'test_video_1',
                'url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'aspect_ratio' => '16:9',
                'title' => 'Test Video 1',
                'order' => 0
            ],
            [
                'id' => 'test_video_2',
                'url' => 'https://www.youtube.com/watch?v=oHg5SJYRHA0',
                'aspect_ratio' => '16:9',
                'title' => 'Test Video 2',
                'order' => 1
            ]
        ];
        
        update_post_meta($test_product_id, '_woo_custom_videos_data', $test_videos);
        echo '<p class="success">✓ Test video data added to product</p>';
        
        // Test getting videos
        $videos = $video_class->get_product_videos($test_product_id);
        echo '<p class="success">✓ Retrieved ' . count($videos) . ' videos from product</p>';
        ?>
    </div>
    
    <div class="test-section">
        <h2>6. Admin URL</h2>
        <?php
        $admin_url = admin_url('post.php?post=' . $test_product_id . '&action=edit');
        echo '<p class="info">Edit test product: <a href="' . $admin_url . '" target="_blank">' . $admin_url . '</a></p>';
        ?>
    </div>
    
    <div class="test-section">
        <h2>7. Frontend URL</h2>
        <?php
        $product_url = get_permalink($test_product_id);
        echo '<p class="info">View test product: <a href="' . $product_url . '" target="_blank">' . $product_url . '</a></p>';
        ?>
    </div>
    
    <div class="test-section">
        <h2>8. JavaScript/CSS URLs</h2>
        <?php
        echo '<p class="info">Admin JS: ' . WOO_CUSTOM_PLUGIN_URL . 'assets/js/featured-video-admin.js</p>';
        echo '<p class="info">Admin CSS: ' . WOO_CUSTOM_PLUGIN_URL . 'assets/css/featured-video-admin.css</p>';
        echo '<p class="info">Frontend JS: ' . WOO_CUSTOM_PLUGIN_URL . 'assets/js/featured-video-frontend.js</p>';
        echo '<p class="info">Frontend CSS: ' . WOO_CUSTOM_PLUGIN_URL . 'assets/css/featured-video-frontend.css</p>';
        ?>
    </div>
    
    <div class="test-section">
        <h2>9. Debug Information</h2>
        <?php
        // Check if videos are properly saved
        $videos = $video_class->get_product_videos($test_product_id);
        echo '<p class="info">Videos in database: <pre>' . print_r($videos, true) . '</pre></p>';

        // Check hooks
        echo '<p class="info">Frontend hooks registered:</p>';
        echo '<ul>';
        if (has_action('wp_footer')) {
            echo '<li class="success">✓ wp_footer hook registered</li>';
        } else {
            echo '<li class="error">✗ wp_footer hook not registered</li>';
        }

        if (has_filter('woocommerce_single_product_image_gallery_classes')) {
            echo '<li class="success">✓ gallery classes filter registered</li>';
        } else {
            echo '<li class="error">✗ gallery classes filter not registered</li>';
        }
        echo '</ul>';
        ?>
    </div>

    <div class="test-section">
        <h2>10. Next Steps</h2>
        <ol>
            <li>Go to the admin product edit page using the link above</li>
            <li>Look for the "Urun Videolari" section below the product gallery</li>
            <li>Click "Video Linki Yukle" button to test the modal</li>
            <li>Add a YouTube or Vimeo URL and test the functionality</li>
            <li>Check the frontend product page to see videos in gallery</li>
            <li>Open browser developer tools to check for JavaScript errors</li>
            <li>Look for video elements being added to the gallery slides</li>
        </ol>
    </div>
    
</body>
</html>
