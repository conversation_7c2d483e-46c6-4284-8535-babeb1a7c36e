/**
 * WooCustom Featured Video Admin JavaScript
 * 
 * @package WooCustom
 * @since 1.0.0
 */

jQuery(document).ready(function($) {
    'use strict';
    
    var videoModal = {
        $modal: null,
        $form: null,
        
        init: function() {
            this.$modal = $('#woo-custom-video-modal');
            this.$form = $('#woo-custom-video-form');
            this.bindEvents();
            this.initSortable();
        },
        
        bindEvents: function() {
            var self = this;
            
            // Open modal
            $(document).on('click', '#add-video-btn', function(e) {
                e.preventDefault();
                self.openModal();
            });
            
            // Close modal
            $(document).on('click', '.woo-custom-modal-close, #add-video-cancel', function(e) {
                e.preventDefault();
                self.closeModal();
            });
            
            // Close modal on outside click
            $(document).on('click', '.woo-custom-modal', function(e) {
                if (e.target === this) {
                    self.closeModal();
                }
            });
            
            // Submit form
            $(document).on('click', '#add-video-submit', function(e) {
                e.preventDefault();
                self.submitForm();
            });
            
            // Remove video
            $(document).on('click', '.remove-video', function(e) {
                e.preventDefault();
                self.removeVideo($(this));
            });
            
            // ESC key to close modal
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27 && self.$modal.is(':visible')) {
                    self.closeModal();
                }
            });
        },
        
        initSortable: function() {
            // Wait for the video gallery section to be available
            setTimeout(function() {
                $('#woo-custom-video-gallery-list').sortable({
                    items: 'li',
                    cursor: 'move',
                    opacity: 0.8,
                    placeholder: 'video-placeholder',
                    update: function(event, ui) {
                        videoModal.updateVideoOrder();
                    }
                });
            }, 500);
        },
        
        openModal: function() {
            this.$modal.fadeIn(300);
            this.$form[0].reset();
            $('#video_url').focus();
        },
        
        closeModal: function() {
            this.$modal.fadeOut(300);
            this.$form[0].reset();
        },
        
        submitForm: function() {
            var self = this;
            var formData = {
                action: 'woo_custom_add_video',
                nonce: wooCustomVideo.nonce,
                product_id: wooCustomVideo.product_id,
                video_url: $('#video_url').val(),
                aspect_ratio: $('#video_aspect_ratio').val(),
                title: $('#video_title').val()
            };
            
            // Validate URL
            if (!formData.video_url) {
                alert(wooCustomVideo.strings.error_url_required);
                $('#video_url').focus();
                return;
            }
            
            if (!this.isValidVideoUrl(formData.video_url)) {
                alert(wooCustomVideo.strings.error_invalid_url);
                $('#video_url').focus();
                return;
            }
            
            // Show loading
            $('#add-video-submit').prop('disabled', true).text('Ekleniyor...');
            
            $.ajax({
                url: wooCustomVideo.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        $('#woo-custom-video-gallery-list').append(response.data.html);
                        self.closeModal();
                        self.showNotice('Video basariyla eklendi.', 'success');
                    } else {
                        alert(response.data || 'Bir hata olustu.');
                    }
                },
                error: function() {
                    alert('AJAX hatasi olustu.');
                },
                complete: function() {
                    $('#add-video-submit').prop('disabled', false).text(wooCustomVideo.strings.add_button);
                }
            });
        },
        
        removeVideo: function($button) {
            if (!confirm(wooCustomVideo.strings.remove_confirm)) {
                return;
            }
            
            var $item = $button.closest('.woo-custom-video-gallery-item');
            var videoId = $item.data('video-id');
            
            $.ajax({
                url: wooCustomVideo.ajax_url,
                type: 'POST',
                data: {
                    action: 'woo_custom_remove_video',
                    nonce: wooCustomVideo.nonce,
                    product_id: wooCustomVideo.product_id,
                    video_id: videoId
                },
                success: function(response) {
                    if (response.success) {
                        $item.fadeOut(300, function() {
                            $(this).remove();
                        });
                        videoModal.showNotice('Video silindi.', 'success');
                    } else {
                        alert(response.data || 'Silme islemi basarisiz.');
                    }
                },
                error: function() {
                    alert('AJAX hatasi olustu.');
                }
            });
        },
        
        updateVideoOrder: function() {
            var videoIds = [];
            $('#woo-custom-video-gallery-list li').each(function() {
                videoIds.push($(this).data('video-id'));
            });
            
            $.ajax({
                url: wooCustomVideo.ajax_url,
                type: 'POST',
                data: {
                    action: 'woo_custom_reorder_videos',
                    nonce: wooCustomVideo.nonce,
                    product_id: wooCustomVideo.product_id,
                    video_ids: videoIds
                },
                success: function(response) {
                    if (response.success) {
                        videoModal.showNotice('Video sirasi guncellendi.', 'success');
                    }
                }
            });
        },
        
        isValidVideoUrl: function(url) {
            var youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/;
            var vimeoRegex = /vimeo\.com\/(\d+)/;
            
            return youtubeRegex.test(url) || vimeoRegex.test(url);
        },
        
        showNotice: function(message, type) {
            var noticeClass = type === 'success' ? 'notice-success' : 'notice-error';
            var $notice = $('<div class="notice ' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');
            
            $('.wrap h1').after($notice);
            
            setTimeout(function() {
                $notice.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        }
    };
    
    // Initialize when DOM is ready
    videoModal.init();
    
    // Debug
    console.log('WooCustom Video Admin initialized');
});
